<script lang="ts">
	import {
		missionState,
		missionCompletionPercentages,
		allMissionsComplete
	} from '../missions/mission.global.svelte.js';
	import { getDailyMissions, getMissionRoute, getMissionButtonText } from '../missions/missionCatalog.js';
	import ProgressBar from '../analysis/ProgressBar.svelte';
	import { H2, H3, H4, H5, P2, P3, Button } from '../ui/index.js';
	import { goto } from '$app/navigation';

	const dailyMissions = getDailyMissions();

	function getMissionProgress(missionId: string): { current: number; target: number; percentage: number } {
		const mission = dailyMissions.find(m => m.id === missionId);
		if (!mission) {
			return { current: 0, target: 0, percentage: 0 };
		}

		const current = missionState.progress?.missions[missionId] ?? 0;
		const percentage = missionCompletionPercentages()[missionId] ?? 0;

		return {
			current,
			target: mission.target,
			percentage
		};
	}

	function handleMissionClick(missionId: string): void {
		const route = getMissionRoute(missionId);
		goto(route);
	}

	function shouldShowButton(missionId: string): boolean {
		const mission = dailyMissions.find(m => m.id === missionId);
		if (!mission?.route || !mission?.buttonText) {
			return false;
		}
		return mission.route.trim() !== '' && mission.buttonText.trim() !== '';
	}

	// Separate completed and incomplete missions
	let incompleteMissions = $derived(dailyMissions.filter(mission => {
		const progress = getMissionProgress(mission.id);
		return progress.current < progress.target;
	}));

	let completedMissions = $derived(dailyMissions.filter(mission => {
		const progress = getMissionProgress(mission.id);
		return progress.current >= progress.target;
	}));
</script>

<div class="daily-missions-card">
	<div class="header">
		<H2>Daily Missions</H2>
		{#if allMissionsComplete()}
			<div class="completion-badge">
				<svg class="completion-icon" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
				</svg>
				<P3 isBold={true} --text-color="var(--aquamarine)">All Complete!</P3>
			</div>
		{/if}
	</div>

	{#if missionState.loading}
		<div class="loading-state">
			<div class="loading-spinner"></div>
		</div>
	{:else if missionState.error}
		<div class="error-state">
			<P2 --text-color="var(--rose)">{missionState.error}</P2>
		</div>
	{:else if missionState.progress}
		{#if allMissionsComplete()}
			<!-- Show only celebration when all missions are complete -->
			<div class="celebration-card">
				<div class="celebration-content">
					<svg class="celebration-icon" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
					</svg>
					<H3 --text-color="var(--aquamarine)">All missions complete!</H3>
					<P2>Great work! Check back tomorrow for new missions.</P2>
				</div>
			</div>
		{:else}
			<div class="missions-container">
				<!-- Incomplete missions (expanded) -->
				{#if incompleteMissions.length > 0}
					<div class="incomplete-missions-section">
						{#if completedMissions.length > 0}
							<H4>Remaining ({incompleteMissions.length})</H4>
						{/if}
						<div class="missions-list">
							{#each incompleteMissions as mission}
								{@const progress = getMissionProgress(mission.id)}

								<div class="mission-item incomplete">
									<div class="mission-header-compact">
										<div class="mission-title-progress">
											<H3>{mission.name}</H3>
											<P3 isBold={true}>
												{progress.current}/{progress.target}
											</P3>
										</div>
									</div>

									<div class="mission-details">
										<P2>{mission.description}</P2>

										<ProgressBar
											percentage={progress.percentage}
											--height=1.25rem
											--light-color="var(--white)"
											--color="var(--sky-blue)"
										/>
									</div>



									{#if shouldShowButton(mission.id)}
										<div class="mission-action">
											<Button onclick={() => handleMissionClick(mission.id)}>
												{getMissionButtonText(mission.id)}
											</Button>
										</div>
									{/if}
								</div>
							{/each}
						</div>
					</div>
				{/if}

				<!-- Completed missions (compact) -->
				{#if completedMissions.length > 0}
					<div class="completed-missions-section">
						<H4 --text-color="var(--aquamarine)">Completed ({completedMissions.length})</H4>
						<div class="completed-missions-list">
							{#each completedMissions as mission}
								<div class="completed-mission-item">
									<svg class="completion-check-small" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
									</svg>
									<H5>{mission.name}</H5>
								</div>
							{/each}
						</div>
					</div>
				{/if}
			</div>
		{/if}
	{:else}
		<div class="no-data-state">
			<P2>No mission data available</P2>
		</div>
	{/if}
</div>

<style>
	.daily-missions-card {
		background-color: var(--white);
		border: 1px solid var(--pitch-black);
		border-radius: 1rem;
		padding: 1.5rem;
		width: 100%;
		height: fit-content;
	}

	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 1.5rem;
	}

	.completion-badge {
		display: flex;
		align-items: center;
		gap: 0.5rem;
	}

	.completion-icon {
		width: 1.25rem;
		height: 1.25rem;
		color: var(--aquamarine);
	}

	.loading-state {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 2rem 0;
	}

	.loading-spinner {
		width: 2rem;
		height: 2rem;
		border-top: 2px solid var(--sky-blue);
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.error-state,
	.no-data-state {
		text-align: center;
		padding: 2rem 0;
	}

	.missions-container {
		display: flex;
		flex-direction: column;
		gap: 1rem;
	}

	.completed-missions-section {
		margin-bottom: 0.5rem;
	}

	.completed-missions-list {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
		margin-top: 0.5rem;
	}

	.completed-mission-item {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.5rem;
		background-color: var(--light-aquamarine);
		border: 1px solid var(--aquamarine);
		border-radius: 0.5rem;
	}

	.completion-check-small {
		width: 1rem;
		height: 1rem;
		color: var(--aquamarine);
		flex-shrink: 0;
	}

	.incomplete-missions-section {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
	}

	.missions-list {
		display: flex;
		flex-direction: column;
		gap: 1rem;
	}

	.mission-item {
		border: 1px solid var(--pitch-black);
		border-radius: 0.75rem;
		padding: 1rem;
		background-color: var(--white);
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
	}

	.mission-item.incomplete {
		background-color: var(--very-light-sky-blue);
	}

	.mission-header-compact {
		margin-bottom: 0.5rem;
	}

	.mission-title-progress {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.mission-details {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.mission-action {
		margin-top: 0.75rem;
		display: flex;
		justify-content: flex-end;
	}

	.celebration-card {
		padding: 1.25rem;
		background-color: var(--light-aquamarine);
		border: 1px solid var(--aquamarine);
		border-radius: 0.75rem;
		text-align: center;
	}

	.celebration-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 0.5rem;
	}

	.celebration-icon {
		width: 1.5rem;
		height: 1.5rem;
		color: var(--aquamarine);
	}
</style>
