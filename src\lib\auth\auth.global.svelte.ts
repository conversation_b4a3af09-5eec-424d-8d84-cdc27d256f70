import { getAuth, onAuthStateChanged, type User } from "firebase/auth";
import { browser } from '$app/environment';
import { app } from '../firebase/config';

// Initialize Firebase Authentication
export const auth = getAuth(app);

// Unified authentication global state
export const authState = $state({
  // Server-side authentication (immediate)
  serverUid: null as string | null,
  serverRole: null as string | null,
  
  // Client-side Firebase authentication (delayed)
  firebaseUser: null as User | null,
  
  // Resolution state
  isFirebaseReady: false,
  authResolved: false
});

// Unified user ID resolution with priority logic
export function resolvedUserId() {
  // Once Firebase is ready, prefer Firebase user
  if (authState.isFirebaseReady && authState.firebaseUser) {
    return authState.firebaseUser.uid;
  }

  // Fallback to server UID when Firebase isn't ready yet
  if (authState.serverUid && !authState.isFirebaseReady) {
    return authState.serverUid;
  }

  return null;
}

// Authentication readiness indicator
export function isAuthReady() {
  return authState.isFirebaseReady || authState.serverUid !== null;
}

// Current user (for compatibility with existing code)
export function user() {
  return authState.firebaseUser;
}

// Initialize authentication state
export function initializeAuthState() {
  // Set up Firebase auth listener
  if (browser && auth) {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      authState.firebaseUser = user;
      authState.isFirebaseReady = true;
      authState.authResolved = true;
    });
    
    return unsubscribe;
  }
  
  return () => {};
}

// Set server authentication data (called from layouts)
export function setServerAuthData(uid: string | null, role: string | null) {
  authState.serverUid = uid;
  authState.serverRole = role;
}
