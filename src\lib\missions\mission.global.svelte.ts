import { onSnapshot, doc, type Unsubscribe } from 'firebase/firestore';
import { db } from '../firebase/firestore.js';
import { resolvedUserId, isAuthReady } from '../auth/auth.global.svelte.js';
import type { DailyMissionProgress, UserStreakData, MissionMetric } from '../types/mission.types.js';
import { getTodayKey, initializeDailyProgress, migrateMissionProgress, checkLoginMissions } from './missionEngine.js';
import { getDailyMissions } from './missionCatalog.js';

// Mission global state - replaces all stores
export const missionState = $state({
  progress: null as DailyMissionProgress | null,
  streakData: null as UserStreakData | null,
  loading: true,
  error: null as string | null,
  lastUpdated: null as Date | null,
  subscriptionsActive: false
});

// Login mission checking state
export const loginMissionState = $state({
  checkedToday: false,
  lastCheckDate: null as string | null,
  checkInProgress: false
});

// Replace derived stores with $derived runes
export const missionCompletionPercentages = $derived(() => {
  if (!missionState.progress) return {};
  
  const dailyMissions = getDailyMissions();
  const percentages: Record<string, number> = {};
  
  dailyMissions.forEach(mission => {
    const current = missionState.progress!.missions[mission.id] || 0;
    percentages[mission.id] = Math.min((current / mission.target) * 100, 100);
  });
  
  return percentages;
});

export const allMissionsComplete = $derived(() => {
  if (!missionState.progress) return false;
  
  const dailyMissions = getDailyMissions();
  return dailyMissions.every(mission => {
    const currentProgress = missionState.progress!.missions[mission.id] || 0;
    return currentProgress >= mission.target;
  });
});

// Subscription management variables
let progressUnsubscribe: Unsubscribe | null = null;
let streakUnsubscribe: Unsubscribe | null = null;

// Initialize mission subscriptions using $effect
export function initializeMissionSubscriptions() {
  // Mission progress subscription effect
  $effect(() => {
    const userId = resolvedUserId;
    
    if (!userId) {
      // Clean up when no user
      if (progressUnsubscribe) {
        progressUnsubscribe();
        progressUnsubscribe = null;
      }
      
      missionState.progress = null;
      missionState.loading = true;
      missionState.subscriptionsActive = false;
      return;
    }
    
    // Clean up previous subscription
    if (progressUnsubscribe) {
      progressUnsubscribe();
    }
    
    missionState.loading = true;
    missionState.error = null;
    
    const todayKey = getTodayKey();
    const progressRef = doc(db, 'users', userId, 'missionProgress', todayKey);
    
    progressUnsubscribe = onSnapshot(
      progressRef,
      async (doc) => {
        try {
          let data: DailyMissionProgress;
          
          if (doc.exists()) {
            data = doc.data() as DailyMissionProgress;
            data = await migrateMissionProgress(userId, data);
          } else {
            data = await initializeDailyProgress(userId);
          }
          
          missionState.progress = data;
          missionState.loading = false;
          missionState.lastUpdated = new Date();
          missionState.subscriptionsActive = true;
        } catch (error) {
          console.error('Error loading mission progress:', error);
          missionState.error = 'Failed to load mission progress';
          missionState.loading = false;
        }
      },
      (error) => {
        console.error('Error subscribing to mission progress:', error);
        missionState.error = 'Failed to subscribe to mission progress';
        missionState.loading = false;
      }
    );
  });
  
  // Streak data subscription effect
  $effect(() => {
    const userId = resolvedUserId;
    
    if (!userId) {
      if (streakUnsubscribe) {
        streakUnsubscribe();
        streakUnsubscribe = null;
      }
      missionState.streakData = null;
      return;
    }
    
    if (streakUnsubscribe) {
      streakUnsubscribe();
    }
    
    const streakRef = doc(db, 'users', userId, 'missionProgress', 'streakData');
    
    streakUnsubscribe = onSnapshot(
      streakRef,
      (doc) => {
        try {
          if (doc.exists()) {
            missionState.streakData = doc.data() as UserStreakData;
          } else {
            missionState.streakData = {
              currentStreak: 0,
              bestStreak: 0
            };
          }
        } catch (error) {
          console.error('Error loading streak data:', error);
          missionState.streakData = {
            currentStreak: 0,
            bestStreak: 0
          };
        }
      },
      (error) => {
        console.error('Error subscribing to streak data:', error);
        missionState.streakData = {
          currentStreak: 0,
          bestStreak: 0
        };
      }
    );
  });
  
  // Cleanup function
  return () => {
    if (progressUnsubscribe) {
      progressUnsubscribe();
      progressUnsubscribe = null;
    }
    if (streakUnsubscribe) {
      streakUnsubscribe();
      streakUnsubscribe = null;
    }
  };
}

// Initialize coordinated login mission checking
export function initializeLoginMissionChecking() {
  // Effect that runs when authentication is resolved
  $effect(() => {
    const userId = resolvedUserId;
    const today = getTodayKey();

    if (!userId || !isAuthReady) return;

    // Prevent duplicate checks
    if (loginMissionState.checkedToday && loginMissionState.lastCheckDate === today) {
      return;
    }

    // Prevent concurrent checks
    if (loginMissionState.checkInProgress) return;

    // Perform login mission check
    (async () => {
      loginMissionState.checkInProgress = true;

      try {
        await checkLoginMissions(userId);
        loginMissionState.checkedToday = true;
        loginMissionState.lastCheckDate = today;
      } catch (error) {
        console.error('Failed to check login missions:', error);
      } finally {
        loginMissionState.checkInProgress = false;
      }
    })();
  });
}

// Cleanup function for mission subscriptions (for compatibility)
export function cleanupMissionSubscriptions(): void {
  if (progressUnsubscribe) {
    progressUnsubscribe();
    progressUnsubscribe = null;
  }
  if (streakUnsubscribe) {
    streakUnsubscribe();
    streakUnsubscribe = null;
  }
}
