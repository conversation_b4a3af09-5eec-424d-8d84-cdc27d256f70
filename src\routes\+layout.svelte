<script lang="ts">
    import '../app.css';
    import { onNavigate } from '$app/navigation';
	import { page } from '$app/state';
    import { user, setServerAuthData, initializeAuthState } from '$lib/auth/auth.global.svelte.js';
	import posthog from 'posthog-js';
	import type { Snippet } from 'svelte';

    let { children }: { children: Snippet } = $props();

    // Set server auth data from page data
    $effect(() => {
        setServerAuthData(page.data.uid, page.data.role);
    });

    // Initialize auth state once
    let authCleanup: (() => void) | null = null;
    $effect(() => {
        if (!authCleanup) {
            authCleanup = initializeAuthState();
        }

        return () => {
            if (authCleanup) {
                authCleanup();
                authCleanup = null;
            }
        };
    });

    $effect(() => {
        if (user) {
            posthog.identify(user.uid, {
                email: user.email,
                name: user.displayName,
            })
        }
    })

    $effect(() => {
        if (page.data.affid) {
            posthog.capture('set_affiliate', {
                $set_once: { affid: page.data.affid }
            })
        }
    })
    
    onNavigate((navigation) => {
        if (!document.startViewTransition) return;

        return new Promise((resolve) => {
            document.startViewTransition(async () => {
                resolve();
                await navigation.complete;
            });
        });
    });
</script>

{@render children?.()}