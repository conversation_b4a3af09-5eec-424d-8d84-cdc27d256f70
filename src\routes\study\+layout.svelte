<script lang="ts">
    import { page } from "$app/state";
    import { setServerAuthData, initializeAuthState } from "$lib/auth/auth.global.svelte.js";
    import {
        initializeMissionSubscriptions,
        initializeLoginMissionChecking
    } from "$lib/missions/mission.global.svelte.js";
    import NavBar from "$lib/study/NavBar.svelte";
    import { innerWidth } from "svelte/reactivity/window";

    let { children, data } = $props();
    let currentPath = $derived(page.url.pathname);

    // Set server auth data immediately
    $effect(() => {
        setServerAuthData(data.uid, data.role);
    });

    // Initialize authentication and mission system once
    let cleanup: (() => void) | null = null;
    $effect(() => {
        if (!cleanup) {
            // Initialize auth state
            const authCleanup = initializeAuthState();

            // Initialize mission system
            const missionCleanup = initializeMissionSubscriptions();
            initializeLoginMissionChecking();

            cleanup = () => {
                authCleanup();
                missionCleanup();
            };
        }

        return () => {
            if (cleanup) {
                cleanup();
                cleanup = null;
            }
        };
    });
</script>

<NavBar {currentPath} role={data.role}/>

<div class="children-container" class:mobile={innerWidth.current < 1024}>
    {@render children()}
</div>

<style>
    .children-container {
        margin: 0 auto;
        margin-left: 4.75rem;
    }

    .mobile {
        margin-left: 0;
        margin-top: 4rem; /* Account for fixed mobile top navbar */
    }
</style>

